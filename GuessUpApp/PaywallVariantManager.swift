//
//  PaywallVariantManager.swift
//  GuessUpApp
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 21.03.2025.
//

import Foundation
import FirebaseRemoteConfig
import FirebaseAnalytics

enum PaywallVariant: String, CaseIterable {
    case original = "original"
    case trialToggle = "trial_toggle"
    case socialProof = "social_proof"
    case valueFirst = "value_first"
    case honest = "honest"
    case personalized = "personalized"
    
    var displayName: String {
        switch self {
        case .original:
            return "Original"
        case .trialToggle:
            return "Trial Toggle"
        case .socialProof:
            return "Social Proof"
        case .valueFirst:
            return "Value First"
        case .honest:
            return "Honest"
        case .personalized:
            return "Personalized"
        }
    }
}

@MainActor
class PaywallVariantManager: ObservableObject {
    static let shared = PaywallVariantManager()
    
    @Published var currentVariant: PaywallVariant = .original
    @Published var showSocialProof: Bool = true
    @Published var trialDurationDays: Int = 7
    @Published var discountPercentage: Int = 50
    @Published var paywallTitle: String = "Unlock Premium Features"
    @Published var paywallSubtitle: String = "Get unlimited access to all categories"
    
    private let remoteConfig = RemoteConfig.remoteConfig()
    private let userDefaults = UserDefaults.standard
    
    // A/B Test tracking
    private let variantKey = "paywall_variant_assigned"
    private let testStartDateKey = "ab_test_start_date"
    
    private init() {
        loadVariantFromRemoteConfig()
        assignUserToVariant()
    }
    
    func loadVariantFromRemoteConfig() {
        // Remote Config'den değerleri al
        let variantString = remoteConfig.configValue(forKey: "paywall_variant").stringValue ?? "original"
        currentVariant = PaywallVariant(rawValue: variantString) ?? .original
        
        showSocialProof = remoteConfig.configValue(forKey: "show_social_proof").boolValue
        trialDurationDays = Int(remoteConfig.configValue(forKey: "trial_duration_days").numberValue?.intValue ?? 7)
        discountPercentage = Int(remoteConfig.configValue(forKey: "discount_percentage").numberValue?.intValue ?? 50)
        paywallTitle = remoteConfig.configValue(forKey: "paywall_title").stringValue ?? "Unlock Premium Features"
        paywallSubtitle = remoteConfig.configValue(forKey: "paywall_subtitle").stringValue ?? "Get unlimited access to all categories"
        
        print("Loaded paywall variant: \(currentVariant.rawValue)")
    }
    
    private func assignUserToVariant() {
        // Eğer kullanıcı daha önce bir variant'a atanmışsa, aynı variant'ı kullan
        if let savedVariant = userDefaults.string(forKey: variantKey),
           let variant = PaywallVariant(rawValue: savedVariant) {
            currentVariant = variant
            return
        }
        
        // Yeni kullanıcı için rastgele variant ata
        let allVariants = PaywallVariant.allCases
        let randomVariant = allVariants.randomElement() ?? .original
        
        currentVariant = randomVariant
        userDefaults.set(randomVariant.rawValue, forKey: variantKey)
        userDefaults.set(Date(), forKey: testStartDateKey)
        
        // A/B test assignment'ı logla
        Analytics.logEvent("ab_test_assignment", parameters: [
            "variant": randomVariant.rawValue,
            "test_name": "paywall_optimization_2024"
        ])
        
        print("Assigned user to variant: \(randomVariant.rawValue)")
    }
    
    func refreshRemoteConfig() async {
        do {
            let status = try await remoteConfig.fetch()
            if status == .success {
                try await remoteConfig.activate()
                loadVariantFromRemoteConfig()
            }
        } catch {
            print("Remote Config refresh failed: \(error)")
        }
    }
    
    // Analytics için variant bilgisini döndür
    func getCurrentVariantForAnalytics() -> String {
        return currentVariant.rawValue
    }
    
    // Test süresi kontrolü (30 gün)
    func isTestExpired() -> Bool {
        guard let startDate = userDefaults.object(forKey: testStartDateKey) as? Date else {
            return false
        }
        
        let daysSinceStart = Calendar.current.dateComponents([.day], from: startDate, to: Date()).day ?? 0
        return daysSinceStart > 30
    }
    
    // Manuel variant değiştirme (debug için)
    func setVariant(_ variant: PaywallVariant) {
        currentVariant = variant
        userDefaults.set(variant.rawValue, forKey: variantKey)
        
        Analytics.logEvent("manual_variant_change", parameters: [
            "new_variant": variant.rawValue
        ])
    }
}

// MARK: - Analytics Extension
extension AnalyticsManager {
    func logPaywallShown(variant: String) {
        Analytics.logEvent("paywall_shown", parameters: [
            "variant": variant,
            "test_name": "paywall_optimization_2024"
        ])
    }
    
    func logPaywallDismissed(variant: String, timeSpent: Double) {
        Analytics.logEvent("paywall_dismissed", parameters: [
            "variant": variant,
            "time_spent_seconds": timeSpent,
            "test_name": "paywall_optimization_2024"
        ])
    }
    
    func logPaywallPurchaseAttempt(variant: String) {
        Analytics.logEvent("paywall_purchase_attempt", parameters: [
            "variant": variant,
            "test_name": "paywall_optimization_2024"
        ])
    }
    
    func logPaywallPurchaseSuccess(variant: String, productId: String, revenue: Double) {
        Analytics.logEvent("paywall_purchase_success", parameters: [
            "variant": variant,
            "product_id": productId,
            "revenue": revenue,
            "test_name": "paywall_optimization_2024"
        ])
    }
    
    func logPaywallTrialStarted(variant: String) {
        Analytics.logEvent("paywall_trial_started", parameters: [
            "variant": variant,
            "test_name": "paywall_optimization_2024"
        ])
    }
}
