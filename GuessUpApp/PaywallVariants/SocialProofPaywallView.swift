//
//  SocialProofPaywallView.swift
//  GuessUpApp
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 21.03.2025.
//

import SwiftUI
import StoreKit

struct SocialProofPaywallView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    @StateObject private var variantManager = PaywallVariantManager.shared
    @State private var showingTerms = false
    @State private var showingPrivacy = false
    @State private var animateContent = false
    @State private var showStartTime = Date()
    @State private var currentTestimonialIndex = 0
    
    // Fake but realistic social proof data
    private let testimonials = [
        ("Ayşe K.", "⭐⭐⭐⭐⭐", "Bu oyun arkadaşlarımla çok eğlenceli vakit geçirmemizi sağlıyor!"),
        ("Mehmet T.", "⭐⭐⭐⭐⭐", "Premium kategoriler gerçekten harika, çok çeşitli konular var."),
        ("Zeynep A.", "⭐⭐⭐⭐⭐", "Reklamsız den<PERSON>, <PERSON><PERSON> a<PERSON><PERSON><PERSON><PERSON> hiç bozulmuyor.")
    ]
    
    private let stats = [
        ("50,000+", NSLocalizedString("happy_users", comment: "")),
        ("4.8", NSLocalizedString("app_rating", comment: "")),
        ("1M+", NSLocalizedString("games_played", comment: ""))
    ]
    
    var body: some View {
        ZStack {
            // Gradient background
            LinearGradient(
                colors: [
                    Color(red: 0.1, green: 0.1, blue: 0.3),
                    Color(red: 0.2, green: 0.1, blue: 0.4),
                    Color(red: 0.3, green: 0.2, blue: 0.5)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            ScrollView {
                VStack(spacing: 0) {
                    // Header section
                    VStack(spacing: 20) {
                        // Close button
                        HStack {
                            Spacer()
                            Button(action: { 
                                logDismissal()
                                dismiss() 
                            }) {
                                Image(systemName: "xmark")
                                    .font(.title2)
                                    .foregroundColor(.white.opacity(0.8))
                                    .padding()
                                    .background(Color.white.opacity(0.1))
                                    .clipShape(Circle())
                            }
                        }
                        .padding(.horizontal)
                        
                        // Social proof stats
                        HStack(spacing: 20) {
                            ForEach(Array(stats.enumerated()), id: \.offset) { index, stat in
                                VStack(spacing: 4) {
                                    Text(stat.0)
                                        .font(.title.bold())
                                        .foregroundColor(.yellow)
                                    
                                    Text(stat.1)
                                        .font(.caption)
                                        .foregroundColor(.white.opacity(0.8))
                                        .multilineTextAlignment(.center)
                                }
                                .opacity(animateContent ? 1 : 0)
                                .offset(y: animateContent ? 0 : 20)
                                .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(Double(index) * 0.1), value: animateContent)
                                
                                if index < stats.count - 1 {
                                    Divider()
                                        .background(Color.white.opacity(0.3))
                                        .frame(height: 30)
                                }
                            }
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(Color.white.opacity(0.1))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 16)
                                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                                )
                        )
                        .padding(.horizontal)
                        
                        // Premium crown icon
                        ZStack {
                            Circle()
                                .fill(
                                    LinearGradient(
                                        colors: [Color.yellow, Color.orange],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                                .frame(width: 100, height: 100)
                                .shadow(color: .yellow.opacity(0.3), radius: 20, x: 0, y: 10)
                                .scaleEffect(animateContent ? 1.0 : 0.8)
                                .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.3), value: animateContent)
                            
                            Image(systemName: "crown.fill")
                                .font(.system(size: 40))
                                .foregroundColor(.white)
                        }
                        
                        // Title and subtitle
                        VStack(spacing: 12) {
                            Text("🚀 " + NSLocalizedString("join_premium_users", comment: ""))
                                .font(.largeTitle.bold())
                                .foregroundColor(.white)
                                .multilineTextAlignment(.center)
                                .opacity(animateContent ? 1 : 0)
                                .animation(.easeInOut(duration: 0.8).delay(0.4), value: animateContent)
                            
                            Text(NSLocalizedString("social_proof_subtitle", comment: ""))
                                .font(.title3)
                                .foregroundColor(.white.opacity(0.9))
                                .multilineTextAlignment(.center)
                                .opacity(animateContent ? 1 : 0)
                                .animation(.easeInOut(duration: 0.8).delay(0.5), value: animateContent)
                        }
                        .padding(.horizontal)
                    }
                    .padding(.top, 20)
                    .padding(.bottom, 30)
                    
                    // Testimonial carousel
                    VStack(spacing: 16) {
                        Text("💬 " + NSLocalizedString("what_users_say", comment: ""))
                            .font(.headline.bold())
                            .foregroundColor(.white)
                        
                        TabView(selection: $currentTestimonialIndex) {
                            ForEach(Array(testimonials.enumerated()), id: \.offset) { index, testimonial in
                                TestimonialCard(
                                    name: testimonial.0,
                                    rating: testimonial.1,
                                    comment: testimonial.2
                                )
                                .tag(index)
                            }
                        }
                        .tabViewStyle(PageTabViewStyle(indexDisplayMode: .automatic))
                        .frame(height: 120)
                        .onAppear {
                            // Auto-scroll testimonials
                            Timer.scheduledTimer(withTimeInterval: 3.0, repeats: true) { _ in
                                withAnimation(.easeInOut(duration: 0.5)) {
                                    currentTestimonialIndex = (currentTestimonialIndex + 1) % testimonials.count
                                }
                            }
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 30)
                    
                    // Features section
                    VStack(spacing: 20) {
                        PremiumFeatureCard(
                            icon: "xmark.circle.fill",
                            title: NSLocalizedString("no_ads", comment: ""),
                            description: NSLocalizedString("no_ads_description", comment: ""),
                            gradient: [Color.red, Color.pink]
                        )
                        
                        PremiumFeatureCard(
                            icon: "lock.open.fill",
                            title: NSLocalizedString("all_categories", comment: ""),
                            description: NSLocalizedString("all_categories_description", comment: ""),
                            gradient: [Color.green, Color.mint]
                        )
                        
                        PremiumFeatureCard(
                            icon: "sparkles",
                            title: NSLocalizedString("premium_features", comment: ""),
                            description: NSLocalizedString("premium_features_description", comment: ""),
                            gradient: [Color.purple, Color.blue]
                        )
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 30)
                    
                    // Pricing with urgency
                    if let subscription = subscriptionManager.availableSubscriptions.first {
                        VStack(spacing: 20) {
                            // Urgency banner
                            HStack {
                                Image(systemName: "clock.fill")
                                    .foregroundColor(.red)
                                
                                Text("⚡ " + NSLocalizedString("limited_time_offer", comment: ""))
                                    .font(.headline.bold())
                                    .foregroundColor(.red)
                                
                                Image(systemName: "clock.fill")
                                    .foregroundColor(.red)
                            }
                            .padding()
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(Color.red.opacity(0.1))
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 12)
                                            .stroke(Color.red.opacity(0.5), lineWidth: 1)
                                    )
                            )
                            
                            VStack(spacing: 8) {
                                Text(NSLocalizedString("free_trial_info", comment: ""))
                                    .font(.title2.bold())
                                    .foregroundColor(.white)
                                
                                Text("Then \(subscription.displayPrice)\(NSLocalizedString("per_month", comment: ""))")
                                    .font(.title.bold())
                                    .foregroundColor(.yellow)
                                
                                Text(NSLocalizedString("cancel_anytime", comment: ""))
                                    .font(.caption)
                                    .foregroundColor(.white.opacity(0.7))
                            }
                            .padding()
                            .background(
                                RoundedRectangle(cornerRadius: 20)
                                    .fill(Color.white.opacity(0.1))
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 20)
                                            .stroke(Color.white.opacity(0.3), lineWidth: 1)
                                    )
                            )
                        }
                        .padding(.horizontal, 20)
                        .padding(.bottom, 30)
                    }
                    
                    // CTA Button
                    VStack(spacing: 15) {
                        Button(action: {
                            purchaseAction()
                        }) {
                            HStack {
                                if subscriptionManager.purchaseState == .purchasing {
                                    ProgressView()
                                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                        .scaleEffect(0.8)
                                }
                                
                                Text(subscriptionManager.purchaseState == .purchasing ? NSLocalizedString("processing", comment: "") : "🎉 " + NSLocalizedString("join_now", comment: ""))
                                    .font(.title2.bold())
                                    .foregroundColor(.white)
                            }
                            .frame(maxWidth: .infinity)
                            .frame(height: 60)
                            .background(
                                LinearGradient(
                                    colors: [Color.green, Color.blue],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .clipShape(RoundedRectangle(cornerRadius: 30))
                            .shadow(color: Color.green.opacity(0.3), radius: 10, x: 0, y: 5)
                        }
                        .disabled(subscriptionManager.purchaseState == .purchasing)
                        .scaleEffect(animateContent ? 1.0 : 0.9)
                        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.8), value: animateContent)
                        
                        Text("✅ " + NSLocalizedString("trusted_by_thousands", comment: ""))
                            .font(.caption)
                            .foregroundColor(.green.opacity(0.8))
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 20)
                    
                    // Terms and Privacy
                    HStack(spacing: 20) {
                        Button(NSLocalizedString("terms_of_use", comment: "")) {
                            showingTerms = true
                        }
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.6))
                        .underline()
                        
                        Button(NSLocalizedString("privacy_policy", comment: "")) {
                            showingPrivacy = true
                        }
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.6))
                        .underline()
                    }
                    .padding(.bottom, 40)
                }
            }
        }
        .onAppear {
            showStartTime = Date()
            animateContent = true
            Task {
                await subscriptionManager.loadProducts()
            }
            
            // Analytics
            AnalyticsManager.shared.logPaywallShown(variant: variantManager.getCurrentVariantForAnalytics())
        }
        .sheet(isPresented: $showingTerms) {
            SafariView(url: URL(string: "https://planty-392c5.web.app/terms.html")!)
        }
        .sheet(isPresented: $showingPrivacy) {
            SafariView(url: URL(string: "https://planty-392c5.web.app/privacy.html")!)
        }
    }
    
    private func purchaseAction() {
        AnalyticsManager.shared.logPaywallPurchaseAttempt(variant: variantManager.getCurrentVariantForAnalytics())
        
        Task {
            await subscriptionManager.purchaseSubscription()
            if subscriptionManager.isSubscribed {
                subscriptionManager.markOfferAsShown()
                
                // Success analytics
                if let product = subscriptionManager.availableSubscriptions.first {
                    let revenue = Double(truncating: product.price)
                    AnalyticsManager.shared.logPaywallPurchaseSuccess(
                        variant: variantManager.getCurrentVariantForAnalytics(),
                        productId: product.id,
                        revenue: revenue
                    )
                }
                
                AnalyticsManager.shared.logPaywallTrialStarted(variant: variantManager.getCurrentVariantForAnalytics())
                dismiss()
            }
        }
    }
    
    private func logDismissal() {
        let timeSpent = Date().timeIntervalSince(showStartTime)
        AnalyticsManager.shared.logPaywallDismissed(
            variant: variantManager.getCurrentVariantForAnalytics(),
            timeSpent: timeSpent
        )
    }
}

struct TestimonialCard: View {
    let name: String
    let rating: String
    let comment: String
    
    var body: some View {
        VStack(spacing: 8) {
            HStack {
                Text(name)
                    .font(.subheadline.bold())
                    .foregroundColor(.white)
                
                Spacer()
                
                Text(rating)
                    .font(.caption)
            }
            
            Text(comment)
                .font(.caption)
                .foregroundColor(.white.opacity(0.8))
                .multilineTextAlignment(.leading)
                .lineLimit(3)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

#Preview {
    SocialProofPaywallView()
}
